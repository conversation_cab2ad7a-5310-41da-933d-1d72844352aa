{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"angular-tailwind": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "css"}, "@schematics/angular:application": {"strict": true}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:application", "options": {"outputPath": {"base": "dist/angular-tailwind"}, "index": "src/index.html", "polyfills": ["src/polyfills.ts"], "tsConfig": "tsconfig.app.json", "inlineStyleLanguage": "css", "assets": ["src/favicon.ico", "src/assets"], "styles": ["src/custom-theme.scss", "src/styles.css", "node_modules/leaflet/dist/leaflet.css"], "scripts": ["node_modules/apexcharts/dist/apexcharts.min.js"], "allowedCommonJsDependencies": ["apexcharts"], "browser": "src/main.ts"}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "4mb", "maximumError": "5mb"}, {"type": "anyComponentStyle", "maximumWarning": "150kb", "maximumError": "150kb"}], "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}], "outputHashing": "all", "optimization": {"scripts": true, "styles": true, "fonts": {"inline": false}}}, "development": {"optimization": false, "extractLicenses": false, "sourceMap": true, "namedChunks": true}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"buildTarget": "angular-tailwind:build:production"}, "development": {"buildTarget": "angular-tailwind:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"buildTarget": "angular-tailwind:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "src/test.ts", "polyfills": "src/polyfills.ts", "tsConfig": "tsconfig.spec.json", "karmaConfig": "karma.conf.js", "inlineStyleLanguage": "css", "assets": ["src/favicon.ico", "src/assets"], "styles": ["src/styles.css"], "scripts": ["node_modules/apexcharts/dist/apexcharts.min.js"]}}}}}, "cli": {"analytics": false}}