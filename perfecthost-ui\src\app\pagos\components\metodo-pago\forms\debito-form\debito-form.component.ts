import { Component, Input, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormGroup } from '@angular/forms';

import { MetodoPagoService } from '../../../../services/metodo-pago.service';
import { MetodoPagoRequest, MetodoPagoResponse } from '../../../../domain/metodo-pago.model';

@Component({
  selector: 'app-debito-form',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule],
  templateUrl: './debito-form.component.html',
  styleUrls: ['./debito-form.component.css']
})

export class DebitoFormComponent {
  @Input() form!: FormGroup;

  constructor(private metodoPagoService: MetodoPagoService ) {}

  get formControls() {
    return this.form.controls;
  }

  getErrorMessage(controlName: string): string {
    const control = this.form.get(controlName);
    if (!control || !control.errors) return '';

    if (control.hasError('required')) return 'Este campo es obligatorio';

    if (control.hasError('minlength')) {
      return `Debe tener al menos ${control.errors['minlength'].requiredLength} caracteres`;
    }

    if (control.hasError('pattern')) {
      switch (controlName) {
        case 'numero_tarjeta':
          return 'El número de tarjeta debe tener 16 dígitos';
        case 'fecha_vencimiento':
          return 'Formato de fecha inválido. Use MM/AA';
        case 'cvv':
          return 'El CVV debe tener 3 dígitos';
        default:
          return 'Formato inválido';
      }
    }

    return '';
  }
  onSubmit() {
    if (this.form.valid) {
      const metodoPago: MetodoPagoRequest = {
        tipo: 'credito',
        usuarioId: 1,
        datos: {
          nombreTitular: this.form.value.nombre_titular,
          numeroTarjeta: this.form.value.numero_tarjeta,
          fechaExpiracion: this.form.value.fecha_vencimiento,
          cvv: this.form.value.cvv,
        }
      };
      this.metodoPagoService.guardarMetodoPago(metodoPago).subscribe(
        (response: MetodoPagoResponse ) => {
          console.log('Método de pago guardado', response);
        },
        (error: any) => {
          console.error('Error al guardar el método de pago', error);
        }
      );
      console.log('Formulario enviado', this.form.value);
    } else {
      console.log('Formulario inválido');
    }
  }
}
