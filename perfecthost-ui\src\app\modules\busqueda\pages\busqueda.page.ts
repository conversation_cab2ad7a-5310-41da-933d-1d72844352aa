import { Component } from '@angular/core';
import { BuscadorComponent } from '../components/buscador/buscador.component';
import { FiltrosComponent } from '../components/filtros/filtros.component';
import { ContentCardComponent } from '../../../shared/components/content-card/content-card.component';
import { PageHeaderComponent } from '../../../shared/components/page-header/page-header.component';

@Component({
  selector: 'app-busqueda-page',
  standalone: true,
  imports: [BuscadorComponent, FiltrosComponent, ContentCardComponent, PageHeaderComponent],
  template: `
    <div class="bg-background min-h-screen">
      <div class="container mx-auto px-6 py-8">
        <!-- Header Section -->
        <app-page-header
          title="Búsqueda de Propiedades"
          description="Encuentra el alojamiento perfecto para tu próxima aventura">
        </app-page-header>

        <!-- Content Grid -->
        <div class="grid grid-cols-1 lg:grid-cols-4 gap-6">
          <!-- Filters Sidebar -->
          <div class="lg:col-span-1">
            <app-content-card title="Filtros">
              <app-filtros></app-filtros>
            </app-content-card>
          </div>

          <!-- Search Results -->
          <div class="lg:col-span-3">
            <app-content-card title="Resultados de búsqueda">
              <div slot="actions">
                <span class="text-sm text-muted-foreground">Mostrando propiedades disponibles</span>
              </div>
              <app-buscador></app-buscador>
            </app-content-card>
          </div>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .container {
      max-width: 1400px;
    }
  `]
})
export class BusquedaPage {}
