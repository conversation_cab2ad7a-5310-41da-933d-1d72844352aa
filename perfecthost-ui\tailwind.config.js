/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./src/**/*.{html,ts}",
  ],
  darkMode: 'class',
  theme: {
    extend: {
      fontFamily: {
        'poppins': ['Poppins', 'system-ui', 'sans-serif'],
        'nunito': ['Nunito Sans', 'sans-serif'],
      },
      colors: {
        // Colores básicos
        border: '#E2E8F0',
        background: '#FFFFFF',
        foreground: '#0C1420',
        primary: {
          DEFAULT: '#E11D48',
          foreground: '#FFFFFF',
        },
        destructive: {
          DEFAULT: '#CC0033',
          foreground: '#FAFAFA',
        },
        muted: {
          DEFAULT: '#CFD9E5',
          foreground: '#64748B',
        },
        card: {
          DEFAULT: '#F1F5F9',
          foreground: '#000000',
        },
      },
      animation: {
        'wiggle': 'wiggle 1s ease-in-out infinite',
        'fade-in-down': 'fade-in-down 0.3s ease-out',
        'fade-out-down': 'fade-out-down 0.3s ease-out',
        'fade-in-up': 'fade-in-up 0.3s ease-out',
        'fade-out-up': 'fade-out-up 0.3s ease-out',
      },
      keyframes: {
        wiggle: {
          '0%, 100%': { transform: 'rotate(-3deg)' },
          '50%': { transform: 'rotate(3deg)' },
        },
        'fade-in-down': {
          '0%': { opacity: '0', transform: 'translateY(-10px)' },
          '100%': { opacity: '1', transform: 'translateY(0)' },
        },
        'fade-out-down': {
          'from': { opacity: '1', transform: 'translateY(0px)' },
          'to': { opacity: '0', transform: 'translateY(10px)' },
        },
        'fade-in-up': {
          '0%': { opacity: '0', transform: 'translateY(10px)' },
          '100%': { opacity: '1', transform: 'translateY(0)' },
        },
        'fade-out-up': {
          'from': { opacity: '1', transform: 'translateY(0px)' },
          'to': { opacity: '0', transform: 'translateY(10px)' },
        }
      },
      boxShadow: {
        'custom': '0px 0px 50px 0px rgb(82 63 105 / 15%)',
      },
    },
  },
  plugins: [
    require('@tailwindcss/forms'),
    require('@tailwindcss/typography'),
    require('@tailwindcss/aspect-ratio'),
    require('tailwind-scrollbar'),
  ],
}
