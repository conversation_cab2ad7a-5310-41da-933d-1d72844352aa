version: '3.8'

services:
  # Base de datos PostgreSQL
  postgres:
    image: postgres:15-alpine
    container_name: perfecthost-postgres
    environment:
      POSTGRES_DB: perfecthost
      POSTGRES_USER: perfecthost
      POSTGRES_PASSWORD: perfecthost123
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - perfecthost-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U perfecthost"]
      interval: 30s
      timeout: 10s
      retries: 3

  # API Spring Boot
  api:
    build:
      context: ./perfecthost-api
      dockerfile: Dockerfile
    container_name: perfecthost-api
    environment:
      SPRING_PROFILES_ACTIVE: docker
      SPRING_DATASOURCE_URL: *******************************************
      SPRING_DATASOURCE_USERNAME: perfecthost
      SPRING_DATASOURCE_PASSWORD: perfecthost123
      SPRING_JPA_HIBERNATE_DDL_AUTO: update
      SPRING_JPA_SHOW_SQL: true
    ports:
      - "8080:8080"
    depends_on:
      postgres:
        condition: service_healthy
    networks:
      - perfecthost-network
    restart: unless-stopped

  # Frontend Angular
  frontend:
    build:
      context: ./perfecthost-ui
      dockerfile: Dockerfile
    container_name: perfecthost-frontend
    ports:
      - "4200:80"
    depends_on:
      - api
    networks:
      - perfecthost-network
    restart: unless-stopped

# Volúmenes para persistir datos
volumes:
  postgres_data:

# Red personalizada
networks:
  perfecthost-network:
    driver: bridge
