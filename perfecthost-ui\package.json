{"name": "angular-tailwind", "displayName": "Angular Tailwind", "version": "0.10.1", "description": "Angular & Tailwind CSS Admin Dashboard Starter Kit, Free and Open Source", "homepage": "https://github.com/lannodev/angular-tailwind#readme", "repository": {"type": "git", "url": "git+https://github.com/lannodev/angular-tailwind.git"}, "keywords": ["angular", "tailwind", "starter-kit", "tailwind-template", "dark mode", "open source"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "scripts": {"ng": "ng", "start": "ng serve --open", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test", "test:e2e": "npx playwright test --ui", "prettier": "prettier --config ./.prettierrc --write \"src/{app,environments}/**/*{.ts,.html,.scss,.json}\"", "prettier:verify": "prettier --config ./.prettierrc --check \"src/{app,environments}/**/*{.ts,.html,.scss,.json}\"", "prettier:staged": "pretty-quick --staged", "lint": "ng lint"}, "private": true, "dependencies": {"@angular/animations": "^19.1.4", "@angular/cdk": "^19.2.19", "@angular/common": "^19.1.4", "@angular/compiler": "^19.1.4", "@angular/core": "^19.1.4", "@angular/forms": "^19.1.4", "@angular/material": "^19.2.19", "@angular/platform-browser": "^19.1.4", "@angular/platform-browser-dynamic": "^19.1.4", "@angular/router": "^19.1.4", "@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "@types/leaflet": "^1.9.17", "angular-svg-icon": "^13.0.0", "apexcharts": "^3.35.3", "leaflet": "^1.9.4", "ng-apexcharts": "^1.7.1", "ngx-sonner": "^2.0.1", "rxjs": "~7.4.0", "tslib": "^2.3.0", "zone.js": "~0.15.0"}, "devDependencies": {"@angular-devkit/build-angular": "^19.1.5", "@angular/cli": "^19.1.5", "@angular/compiler-cli": "^19.1.4", "@types/jasmine": "^5.1.8", "@types/node": "^12.11.1", "autoprefixer": "^10.4.7", "jasmine": "^5.8.0", "postcss": "^8.5.1", "prettier": "^2.7.1", "prettier-plugin-tailwindcss": "^0.1.12", "tailwind-scrollbar": "^3.1.0", "tailwindcss": "^3.4.16", "typescript": "~5.7.3"}}