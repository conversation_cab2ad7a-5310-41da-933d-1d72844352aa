spring.application.name=ufro.dci

# Configuración de base de datos PostgreSQL para Docker
spring.datasource.url=*******************************************
spring.datasource.username=perfecthost
spring.datasource.password=perfecthost123
spring.datasource.driver-class-name=org.postgresql.Driver
spring.jpa.database-platform=org.hibernate.dialect.PostgreSQLDialect

# Configuración JPA
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true

# JWT CONFIGURATION
jwt.secret=1234567890
jwt.expiration=3600000

# Configuración del servidor
server.port=8080

# Configuración CORS para permitir conexiones desde el frontend
spring.web.cors.allowed-origins=http://localhost:4200
spring.web.cors.allowed-methods=GET,POST,PUT,DELETE,OPTIONS
spring.web.cors.allowed-headers=*
spring.web.cors.allow-credentials=true

# Configuración de logging
logging.level.org.springframework.web=DEBUG
logging.level.org.hibernate.SQL=DEBUG
logging.level.org.hibernate.type.descriptor.sql.BasicBinder=TRACE
