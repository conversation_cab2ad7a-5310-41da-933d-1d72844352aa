events {
    worker_connections 1024;
}

http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;

    sendfile        on;
    keepalive_timeout  65;

    server {
        listen       4200;
        server_name  localhost;

        root   /usr/share/nginx/html;
        index  index.html index.htm;

        # <PERSON>ejar ruta<PERSON> (SPA)
        location / {
            try_files $uri $uri/ /index.html;
        }

        # Configuración para archivos estáticos
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }

        # Configuración de seguridad básica
        add_header X-Frame-Options "SAMEORIGIN" always;
        add_header X-XSS-Protection "1; mode=block" always;
        add_header X-Content-Type-Options "nosniff" always;
    }
}
