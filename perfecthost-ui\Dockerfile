# Dockerfile para Angular 19 - Servidor de desarrollo
FROM node:20-alpine

# Establecer directorio de trabajo
WORKDIR /app

# Copiar package.json y package-lock.json
COPY package*.json ./

# Instalar dependencias
RUN npm ci

# Copiar código fuente
COPY . .

# Exponer puerto 4200
EXPOSE 4200

# Comando para ejecutar el servidor de desarrollo
CMD ["npm", "run", "start", "--", "--host", "0.0.0.0", "--port", "4200"]
